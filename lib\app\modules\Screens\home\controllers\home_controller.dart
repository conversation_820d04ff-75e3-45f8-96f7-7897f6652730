import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:vropay_final/app/core/models/user_model.dart';
import 'package:vropay_final/app/core/services/auth_service.dart';
import 'package:vropay_final/app/core/services/forum_service.dart';
import 'package:vropay_final/app/core/services/knowledge_service.dart';
import 'package:vropay_final/app/routes/app_pages.dart';

class HomeController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final KnowledgeService _knowledgeService = Get.find<KnowledgeService>();
  final ForumService _forumService = Get.find<ForumService>();

  // Observable variables
  var isLoading = false.obs;
  var user = Rxn<UserModel>();
  var featuredTopics = <Map<String, dynamic>>[].obs;
  var recentTopics = <Map<String, dynamic>>[].obs;
  var communityPosts = <Map<String, dynamic>>[].obs;
  var selectedTopics = <String>[].obs;
  var difficultyLevel = 'Beginner'.obs;
  var communityAccess = 'Public'.obs;
  var notificationsEnabled = true.obs;
  var currentIndex = 0.obs;
  var showUserDetailsForm = false.obs;
  var currentStep = 0
      .obs; // 0: userDetails, 1: interests, 2: difficulty, 3: community, 4: notifications, 5: subscription

  // Fields required by onboarding widgets
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  var selectedRole = ''.obs; // profession
  var selectedLevel = ''.obs; // used by widgets for gender/difficulty selection
  var selectedCommunityAccess =
      ''.obs; // UI label (Join & Interact / Just Scroll)
  var interests = <String>[].obs; // list of selectable interests

  var selectedInterests = <String>[].obs; // selected interests for UI

  @override
  void onInit() {
    super.onInit();

    // Check if user needs to fill details
    final args = Get.arguments as Map<String, dynamic>?;
    if (args?['showUserDetails'] == true) {
      showUserDetailsForm.value = true;
    }

    loadUserData();
    loadFeaturedTopics();
    loadRecentTopics();
    loadCommunityPosts();
    _hydrateInterests();
  }

  // Toggle interest selection for profile/interests dialog
  void toggleInterest(String topic) {
    if (selectedInterests.contains(topic)) {
      selectedInterests.remove(topic);
    } else {
      selectedInterests.add(topic);
    }
  }

  // Load user data from backend
  Future<void> loadUserData() async {
    try {
      isLoading.value = true;
      final response = await _authService.getUserProfile();
      final userData = response.data;
      if (userData != null) {
        user.value = userData;
        selectedTopics.value = userData.selectedTopics ?? [];
        difficultyLevel.value = userData.difficultyLevel ?? 'Beginner';
        communityAccess.value = userData.communityAccess ?? 'Public';
        notificationsEnabled.value = userData.notificationsEnabled ?? true;
      }
    } catch (e) {
      print('Error loading user data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Load featured topics from backend
  Future<void> loadFeaturedTopics() async {
    try {
      final topics = await _knowledgeService.getFeaturedTopics();
      featuredTopics.value = topics;
    } catch (e) {
      print('Error loading featured topics: $e');
    }
  }

  // Load recent topics from backend
  Future<void> loadRecentTopics() async {
    try {
      final topics = await _knowledgeService.getRecentTopics();
      recentTopics.value = topics;
    } catch (e) {
      print('Error loading recent topics: $e');
    }
  }

  // Load community posts from backend
  Future<void> loadCommunityPosts() async {
    try {
      final posts = await _forumService.getCommunityPosts();
      communityPosts.value = posts;
    } catch (e) {
      print('Error loading community posts: $e');
    }
  }

  // Update user preferences
  Future<void> updateUserPreferences() async {
    try {
      isLoading.value = true;
      await _authService.updateUserPreferences(
        selectedTopics: selectedTopics.toList(),
        difficultyLevel: difficultyLevel.value,
        communityAccess: communityAccess.value,
        notificationsEnabled: notificationsEnabled.value,
      );
      Get.snackbar('Success', 'Preferences updated successfully');
    } catch (e) {
      Get.snackbar('Error', 'Failed to update preferences: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Navigate to topic details
  void navigateToTopic(String topicId) {
    Get.toNamed(Routes.KNOWLEDGE_CENTER_SCREEN,
        arguments: {'topicId': topicId});
  }

  // Navigate to community post
  void navigateToCommunityPost(String postId) {
    Get.toNamed(Routes.COMMUNITY_FORUM, arguments: {'postId': postId});
  }

  // Refresh all data
  Future<void> refreshData() async {
    await Future.wait([
      loadUserData(),
      loadFeaturedTopics(),
      loadRecentTopics(),
      loadCommunityPosts(),
    ]);
  }

  // ---------------- Onboarding widget helpers ----------------
  void selectLevel(String level) {
    selectedLevel.value = level;
    // If this reflects difficulty selection, map to difficultyLevel
    if (level == 'Beginner' || level == 'Moderate' || level == 'Advance') {
      difficultyLevel.value = level;
    }
  }

  bool isUserDetailValid() {
    return firstNameController.text.trim().isNotEmpty &&
        lastNameController.text.trim().isNotEmpty &&
        selectedRole.value.trim().isNotEmpty;
  }

  Future<void> updateUserProfile() async {
    try {
      isLoading.value = true;
      // Use selectedLevel as gender if it matches gender labels; fallback to Male
      final String gender = (selectedLevel.value == 'Male' ||
              selectedLevel.value.toLowerCase() == 'female')
          ? selectedLevel.value
          : 'Male';

      await _authService.updateUserProfile(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim(),
        gender: gender,
        profession: selectedRole.value.trim(),
        mobile: null,
        selectedTopics: selectedTopics.toList(),
        difficultyLevel: difficultyLevel.value,
        communityAccess: communityAccess.value,
        notificationsEnabled: notificationsEnabled.value,
      );

      Get.snackbar('Success', 'Profile updated');
    } catch (e) {
      Get.snackbar('Error', 'Failed to update profile: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void updateCommunityAccess(String option) {
    selectedCommunityAccess.value = option;
    communityAccess.value = option.contains('Join') ? 'Public' : 'Private';
  }

  void _hydrateInterests() async {
    await loadInterests();
  }

  Future<void> loadInterests() async {
    try {
      final response = await _authService.getInterests();
      print('Response type: ' + response.runtimeType.toString());
      print('Response: ' + response.toString());

      if (response is Map<String, dynamic> && response['interests'] is List) {
        final interestsList = response['interests'] as List<dynamic>;
        final interestNames = <String>[];

        for (final item in interestsList) {
          final interest = item as Map<String, dynamic>?; // safe cast
          if (interest != null && interest['name'] != null) {
            interestNames.add(interest['name'].toString());
          }
        }

        interests.value = interestNames;
        print('Interests loaded: ${interests.value}');
      }
    } catch (e) {
      print("Error loading interests: $e");
    }
  }

  bool hasSelectedInterests() => selectedInterests.isNotEmpty;

  // Navigate to next step in onboarding flow
  void nextStep() {
    if (currentStep.value < 5) {
      currentStep.value++;
      if (currentStep.value == 1) {
        loadInterests();
      }
    } else {
      // Final step - send all data to backend and go to dashboard
      completeOnboarding();
    }
  }

  // Complete onboarding and send all data to backend
  Future<void> completeOnboarding() async {
    await updateUserProfile();
    Get.offAllNamed(Routes.DASHBOARD);
  }

  @override
  void onClose() {
    firstNameController.dispose();
    lastNameController.dispose();
    super.onClose();
  }
}
